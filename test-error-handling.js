// Test script for error handling function

// Mock the alert function to capture outputs
let alertMessages = [];
global.alert = (message) => {
  alertMessages.push(message);
  console.log('Alert message:', message);
};

// Import the error message display function
function displayErrorMessage(errorData) {
  let errorMessage = 'Error: ';
  
  if (errorData.error) {
    errorMessage += errorData.error;
  }
  
  if (errorData.message) {
    errorMessage += '\n\nMessage: ' + errorData.message;
  }
  
  if (errorData.details) {
    errorMessage += '\n\nDetails: ' + errorData.details;
  }
  
  alert(errorMessage);
}

// Test case 1: Error with all fields
console.log('Test case 1: Error with all fields');
alertMessages = [];
displayErrorMessage({
  error: 'Failed to scrape the content',
  message: 'Connection timeout',
  details: 'Error: Connection timeout while accessing external API'
});

if (alertMessages[0].includes('Failed to scrape the content') &&
    alertMessages[0].includes('Connection timeout') &&
    alertMessages[0].includes('Error: Connection timeout while accessing external API')) {
  console.log('✅ Test case 1 passed');
} else {
  console.log('❌ Test case 1 failed');
  console.log('Expected all error fields, got:', alertMessages[0]);
}

// Test case 2: Error without message field
console.log('\nTest case 2: Error without message field');
alertMessages = [];
displayErrorMessage({
  error: 'Failed to scrape the content',
  details: 'Error: Connection timeout while accessing external API'
});

if (alertMessages[0].includes('Failed to scrape the content') &&
    !alertMessages[0].includes('\n\nMessage:') &&
    alertMessages[0].includes('Error: Connection timeout while accessing external API')) {
  console.log('✅ Test case 2 passed');
} else {
  console.log('❌ Test case 2 failed');
  console.log('Expected error and details only, got:', alertMessages[0]);
}

// Test case 3: Error without details field
console.log('\nTest case 3: Error without details field');
alertMessages = [];
displayErrorMessage({
  error: 'Failed to scrape the content',
  message: 'Connection timeout'
});

if (alertMessages[0].includes('Failed to scrape the content') &&
    alertMessages[0].includes('Connection timeout') &&
    !alertMessages[0].includes('\n\nDetails:')) {
  console.log('✅ Test case 3 passed');
} else {
  console.log('❌ Test case 3 failed');
  console.log('Expected error and message only, got:', alertMessages[0]);
}

// Test case 4: Error with only error field
console.log('\nTest case 4: Error with only error field');
alertMessages = [];
displayErrorMessage({
  error: 'Failed to scrape the content'
});

if (alertMessages[0].includes('Failed to scrape the content') &&
    !alertMessages[0].includes('\n\nMessage:') &&
    !alertMessages[0].includes('\n\nDetails:')) {
  console.log('✅ Test case 4 passed');
} else {
  console.log('❌ Test case 4 failed');
  console.log('Expected only error field, got:', alertMessages[0]);
}

console.log('\nAll tests completed.');