const XT_BE_BASE_URL = 'http://44.201.229.151:8080/api';
const AI_BASE_URL = 'https://qwowsgp5fizewd4gvcofqxfsea0szdqg.lambda-url.us-east-1.on.aws';
const TESTING_NUMBER = "+19298339765";
const USE_TESTING_NUMBER = false;

// Function to display comprehensive error messages
function displayErrorMessage(errorData) {
  let errorMessage = 'Error: ';
  
  if (errorData.error) {
    errorMessage += errorData.error;
  }
  
  if (errorData.message) {
    errorMessage += '\n\nMessage: ' + errorData.message;
  }
  
  if (errorData.details) {
    errorMessage += '\n\nDetails: ' + errorData.details;
  }
  
  alert(errorMessage);
}

// Define the color shades from 50 to 950
const colorShades = {
  50: '#f7fee7',
  100: '#ecfccb',
  200: '#d9f99d',
  300: '#bef264',
  400: '#a3e635',
  500: '#84cc16',
  600: '#65a30d',
  700: '#4d7c0f',
  800: '#3f6212',
  900: '#365314',
  950: '#1a2e05'
};

// Normalize the score to a value between 0 and 1
function normalizeScore(score, minScore, maxScore) {
  return (score - minScore) / (maxScore - minScore);
}

// Assign color based on normalized score
function getColorForScore(normalizedScore) {
  const shadesKeys = Object.keys(colorShades).map(Number);
  const index = Math.min(Math.floor(normalizedScore * shadesKeys.length), shadesKeys.length - 1);
  return colorShades[shadesKeys[index]];
}

// Retrieve user data from chrome storage
chrome.storage.local.get(['user'], (result) => {
  if (chrome.runtime.lastError) {
    console.error(chrome.runtime.lastError.message);
    return;
  }

  const userData = result.user;

  if (!userData || !userData.id) {
    console.error('User is not logged in or user ID is missing');
    return;
  }

  const userId = userData.id; // Extract user ID

  // Add event listener for the scrapeButton
  document.getElementById('scrapeButton').addEventListener('click', async () => {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    const scrapeButton = document.getElementById('scrapeButton');
    scrapeButton.innerText = 'Finding Loads...'; // Change button text
    scrapeButton.disabled = true; // Disable the button

    // Execute script in the active tab to scrape HTML
    chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: () => document.documentElement.innerHTML
    }, (results) => {
      if (chrome.runtime.lastError) {
        console.error('Script execution error:', chrome.runtime.lastError.message);
        return;
      }

      if (!results || !results.length) {
        console.error('No results returned from script execution');
        return;
      }

      const htmlContent = results[0].result;
      console.log('Scraped HTML:', htmlContent);

      // Send scraped HTML, current URL, and user ID to the server
      fetch(`${XT_BE_BASE_URL}/loads/scrape`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          html: htmlContent,
          url: tab.url,   // Current tab's URL
          userId: userId  // User ID sent as part of the request
        })
      })
        .then(response => response.json())
        .then(data => {
          console.log('Server response:', data);

          // Check if there's an error in the response
          if (data.error) {
            displayErrorMessage(data);
            scrapeButton.innerText = 'AI Loads Finder'; // Reset button text
            scrapeButton.disabled = false; // Re-enable the button
            return;
          }

          // Assuming 'loads' is part of the response data
          const loads = data.loads;  // Extract loads from the response (adjust if 'loads' is different)

          if (!loads) {
            console.error('No loads data received from the backend');
            return;
          }

          const blob = new Blob([JSON.stringify(loads)], { type: 'application/json' });

          // Create a new FormData object and append the Blob as a file
          const formData = new FormData();
          formData.append('loads_data', blob, 'data.json');

          // Send the loads to the prioritization API
          fetch(`${AI_BASE_URL}/prioritization`, {
            method: 'POST',
            body: formData
          })
            .then(response => response.json())
            .then(prioritizationResponse => {
              const loads = prioritizationResponse.load_data;

              scrapeButton.innerText = 'AI Loads Finder'; // Change button text
              scrapeButton.disabled = false; // Disable the button
              if (!loads || !loads.length) {
                console.error('No loads data received or loads are empty.');
                return;
              }


              // Execute script in the active tab to scrape load items
              chrome.scripting.executeScript({
                target: { tabId: tab.id },
                func: () => {
                  // Function that will be executed in the context of the active tab
                  const loadElements = document.querySelectorAll('#load_item');  // Get load elements by ID
                  return Array.from(loadElements).map(element => element.outerHTML);  // Return the HTML of each element
                }
              }, (results) => {
                if (chrome.runtime.lastError) {
                  console.error('Script execution error:', chrome.runtime.lastError.message);
                  return;
                }

                if (!results || !results.length) {
                  console.error('No load elements found on the page');
                  return;
                }

                // Log the HTML of the load elements (for debugging)

                const loadElementsHtml = results[0].result
                const scores = loads
                  .map(load => load?.score?.total_score)  // Optional chaining to safely access total_score
                  .filter(score => score !== undefined);  // Filter out any undefined scores


                const minScore = Math.min(...scores);
                const maxScore = Math.max(...scores);



                loadElementsHtml.forEach(htmlString => {
                  // Convert HTML string back into DOM element


                  // Create a temporary DOM element
                  const mainDiv = document.createElement('div');
                  mainDiv.innerHTML = htmlString; // Set the HTML string

                  // Now you can use querySelector on the mainDiv
                  const deadhead = mainDiv.querySelector('#deadhead')?.innerText;
                  const matchingLoad = prioritizationResponse.load_data.find(load => {
                    // Check that all properties match
                    const deadheadMatch = (load.deadhead || '').trim() === (deadhead || '').trim();
                    // Return true only if all properties match
                    return deadheadMatch;
                  });
                  if (matchingLoad) {
                    const normalizedScore = normalizeScore(matchingLoad.score.total_score, minScore, maxScore);
                    const color = getColorForScore(normalizedScore);  // Use your existing color scoring function if needed.

                    // Highlight the matching load in green
                    chrome.scripting.executeScript({
                      target: { tabId: tab.id },
                      func: (matchingDeadhead) => {
                        // Find the load item by deadhead and apply green background color
                        const loadElements = document.querySelectorAll('#load_item');  // Get load elements by ID


                        loadElements.forEach(element => {
                          const deadhead = element.querySelector('#deadhead')?.innerText;

                          // If deadhead matches, highlight this load
                          if (deadhead === matchingDeadhead) {
                            element.style.setProperty('background-color', '#a3e635', 'important');  // Green background with !important
                            element.style.setProperty('border-top', '4px solid #a3e635', 'important');  // Green border-top with !important (adjust thickness)
                          }
                        });
                      },
                      args: [matchingLoad.deadhead]  // Pass the deadhead of the matching load
                    });
                  }
                });
              })

            })
            .catch(error => console.error('Error sending loads to prioritization API:', error));
        })
        .catch(error => {
          console.error('Error sending HTML to server:', error);
          // Create an error object similar to the API's error format
          const errorData = {
            error: 'Failed to connect to the server',
            message: error.message,
            details: error.toString()
          };
          displayErrorMessage(errorData);
          
          // Reset button state
          scrapeButton.disabled = false;
          scrapeButton.innerText = 'AI Loads Finder';
        });
    });
  });
});


// Function to extract key-value pairs
function extractKeyValuePairs(data) {
  let result = '';
  let callNumber = '';

  function iterate(obj, parentKey = '') {
    for (const [key, value] of Object.entries(obj)) {
      const newKey = parentKey ? `${parentKey}.${key}` : key;

      if (key === 'id' || key === 'createdAt' || key === 'updatedAt' || key === '__v') {
        continue; // Skip unwanted keys
      }

      if (newKey === 'loadsDetails.call') {
        callNumber = value; // Store the call number separately
        continue;
      }

      if (typeof value === 'object' && value !== null) {
        // If value is an object, recursively iterate over it
        iterate(value, newKey);
      } else {
        // If value is a primitive, append it to the result string
        result += `${newKey}: ${value}\n`;
      }
    }
  }

  iterate(data);
  return { result, callNumber }; // Return both result string and call number
}

//current load
chrome.storage.local.get(['user'], (result) => {
  if (chrome.runtime.lastError) {
    console.error(chrome.runtime.lastError.message);
    return;
  }

  const userData = result.user;

  if (!userData || !userData.id) {
    console.error('User is not logged in or user ID is missing');
    return;
  }

  const userId = userData.id; // Extract user ID
  const scrapeButton = document.getElementById('scrapeCurrentButton');

  scrapeButton.addEventListener('click', async () => {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    // Disable the button and change text
    scrapeButton.disabled = true;
    scrapeButton.innerText = 'Negotiating...';

    // Execute script in the context of the active tab
    chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: () => document.documentElement.innerHTML
    }, (results) => {
      if (chrome.runtime.lastError) {
        console.error(chrome.runtime.lastError.message);
        scrapeButton.disabled = true;
        scrapeButton.innerText = 'Scrape Current';
        return;
      }

      const htmlContent = results[0].result;

      // Send the scraped HTML and current tab URL to the server for processing
      fetch(`${XT_BE_BASE_URL}/loads/current`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          "Connection": "keep-alive"
        },
        body: JSON.stringify({
          html: htmlContent,
          url: tab.url,
          userId: userId
        })
      })
        .then(response => response.json())
        .then(data => {
          // Restore the button state
          scrapeButton.disabled = false;
          scrapeButton.innerText = 'Negotiate with AI Call';

          // Handle the server response
          if (data.error) {
            displayErrorMessage(data);
            return;
          }

          // Extract key-value pairs
          // const { result, callNumber } = extractKeyValuePairs(data);
          
          const callNumber = data["loadsDetails"]["call"]
          const result = {
            "rate": data["loadsDetails"]["price"],
            "rate_per_mile": data["loadsDetails"]["pricePerMile"],
            "loaded_miles": data["loadsDetails"]["mileage"],
            "deadhead_miles": data["loadsDetails"]["deadhead"],
            "pickup_location": data["loadsDetails"]["location"],
            "delivery_location": data["loadsDetails"]["destination"],
            "pickup_date": data["loadsDetails"]["dateAndTime"],
            "commodity_type": "",
            "weight": data["loadsDetails"]["weight"],
            "truck_size": data["loadsDetails"]["loadSize"],
            "broker_name": data["companyInformation"]["company"],
            "broker_mc": data["companyInformation"]["mcNumber"],
            "ref": ""
          }
          finalCallNumber = `+1${callNumber}`; // Prepend +1 for US
          if (USE_TESTING_NUMBER) {
            finalCallNumber = TESTING_NUMBER;
          }
          const apiUrl = `${AI_BASE_URL}/create-call?number=${encodeURIComponent(finalCallNumber)}`;
          const requestBody = {
            "load_data": result
          };
          fetch(apiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody) // Ensure body is a valid JSON object
          }).then(response => response.json())
            .then(apiData => {
              // Replace console.log with fetch to send data to another API
              return fetch(`${XT_BE_BASE_URL}/loads/call-logs`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ ...apiData, userId }), // Send the API response data
              });
            })
            .then(response => response.json())
            .then(externalApiData => {
              // Handle the response from the external API as needed
              console.log('External API Response:', externalApiData);
              alert("Call has been initiated, check your dashboard for more!");
            })
            .catch(apiError => {
              console.error('Error sending call request:', apiError);
              // Create an error object similar to the API's error format
              const errorData = {
                error: 'Error initiating call',
                message: apiError.message,
                details: apiError.toString()
              };
              displayErrorMessage(errorData);
            });
        })
        .catch(error => {
          console.error('Error:', error);
          // Create an error object similar to the API's error format
          const errorData = {
            error: 'Failed to connect to the server',
            message: error.message,
            details: error.toString()
          };
          displayErrorMessage(errorData);
          
          // Restore the button state in case of error
          scrapeButton.disabled = false;
          scrapeButton.innerText = 'Negotiate with AI Call';
        });
    });
  });
});

// Function to update the UI based on user authentication state
function updateUI() {
  chrome.storage.local.get(['user'], (result) => {
    if (chrome.runtime.lastError) {
      console.error(chrome.runtime.lastError.message);
      return;
    }
    const userData = result.user;

    if (userData) {
      // User is logged in
      document.getElementById('userInfo').style.display = 'flex'; // Show user info
      document.getElementById('userInfo').style.alignItems = 'center'; // Show user info
      document.getElementById('userInfo').style.gap = '80px'; // Show user info
      document.getElementById('profileImage').src = userData.profile_image; // Set profile image
      document.getElementById('userName').innerText = userData.name; // Set user name
      document.querySelector('.account-avatar').style.display = 'none'; // Hide avatar
      document.getElementById('loginSection').style.display = 'none'; // Hide login button
      document.getElementById('bottom-buttons').style.display = 'block'; // Hide login button
    } else {
      // User is not logged in
      document.getElementById('bottom-buttons').style.display = 'none'; // Hide login button
      document.getElementById('userInfo').style.display = 'none'; // Hide user info
      document.getElementById('loginSection').style.display = 'block'; // Show login button
      document.querySelector('.account-avatar').style.display = 'flex'; // Show avatar
    }
  });
}

// Add event listener to the logout button
document.getElementById('logoutButton').addEventListener('click', () => {
  chrome.storage.local.remove('user', () => {
    if (chrome.runtime.lastError) {
      console.error(chrome.runtime.lastError.message);
      return;
    }
    // Update UI after logout
    updateUI();
  });
});


// Add event listener to the Login button
document.getElementById('loginButton').addEventListener('click', async () => {
  console.log("Login requested.");
  chrome.runtime.sendMessage({ type: 'login-request' });
});

updateUI();