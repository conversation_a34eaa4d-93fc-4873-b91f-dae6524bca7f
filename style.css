/* General Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Body Styling */
body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f7f8fa;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

/* Wallet Container */
.wallet-container {
  width: 350px;
  background-color: white;
  border-radius: 15px;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Account Information */
.account-info {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.account-details {
  display: flex;
  align-items: center;
}

.account-avatar i {
  font-size: 40px;
  color: #333;
  margin-right: 10px;
}

.account-name {
  font-size: 16px;
  font-weight: 600;
}

.account-address {
  font-size: 13px;
  color: #666;
}

.settings-icon i {
  font-size: 20px;
  color: #666;
}

/* Balance Section */
.balance-section {
  width: 100%;
  background-color: #e9d5ff;
  border-radius: 10px;
  text-align: center;
  padding: 20px;
  margin-bottom: 20px;
}

.balance-eth {
  font-size: 32px;
  color: #000;
  margin-bottom: 10px;
}

.balance-usd {
  color: #888;
  font-size: 14px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.receive-button,
.send-button {
  width: 120px;
  padding: 10px;
  border-radius: 5px;
  border: none;
  font-weight: bold;
  cursor: pointer;
}

.receive-button {
  background-color: #ecfccb;
  color: #a3e635;
}

.send-button {
  background-color: #fef3c7;
  color: #facc15;
}

/* Transaction History */
.transaction-history {
  width: 100%;
  margin-bottom: 20px;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 18px;
}

.sent {
  background-color: #ffe8e8;
  color: #ff4d4f;
}

.received {
  background-color: #e8f5fe;
  color: #007bff;
}

.transaction-details {
  flex-grow: 1;
  padding: 0 10px;
}

.transaction-type {
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
}

.transaction-date {
  font-size: 12px;
  color: #888;
}

.transaction-amount {
  text-align: right;
  font-size: 14px;
}

.transaction-amount span {
  font-size: 12px;
  color: #888;
}

/* Blank Deposit and Withdraw Buttons */
.bottom-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.load-button {
  color: black;
  width: 48%;
  padding: 5px;
  border-radius: 5px;
  border: 1px solid #6366f1;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.blank-button:hover {
  background-color: #333;
}
.scrape-button {
  background-color: #6366f1;
  color: white;
  width: 48%;
  padding: 5px;
  border-radius: 5px;
  border: 1px solid white;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

#loginButton,
#logoutButton {
  background-color: #6366f1;
  color: white;

  padding: 5px;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

/* Integrate */
.integrate {
  background-color: #fef9c3;
  color: #facc15;
}
