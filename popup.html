<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Ethereum Wallet UI</title>
    <link rel="stylesheet" href="style.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />
  </head>
  <body>
    <div class="wallet-container">
      <!-- Account Info Section -->
      <div class="account-info">
        <div class="account-details">
          <div class="account-avatar">
            <i class="fas fa-user-circle"></i>
          </div>
          <div id="userInfo" style="display: none">
            <img
              id="profileImage"
              width="50px"
              height="50px"
              style="border-radius: 50px"
              alt="Profile Image"
            />
            <span id="userName"></span>
            <button id="logoutButton">LOGOUT</button>
          </div>
          <div id="loginSection">
            <button id="loginButton">LOGIN</button>
          </div>
        </div>
      </div>

      <!-- Balance Display Section -->
      <div class="balance-section">
        <div class="balance-info">
          <h1 class="balance-eth">XTrucker AI</h1>
          <p class="balance-usd">
            For enhanced dispatching and operational efficiency.
          </p>
        </div>
      </div>

      <!-- Transaction History -->
      <div class="transaction-history">
        <div class="transaction-item">
          <div class="icon-container integrate">
            <i class="fa-solid fa-gears"></i>
          </div>
          <div class="transaction-details">
            <p class="transaction-type">Integrate with XTrucker AI</p>
          </div>
        </div>

        <div class="transaction-item">
          <div class="icon-container received">
            <i class="fa-solid fa-robot"></i>
          </div>
          <div class="transaction-details">
            <p class="transaction-type">Automate Dispatching</p>
          </div>
        </div>

        <div class="transaction-item">
          <div class="icon-container sent">
            <i class="fa-solid fa-hand-sparkles"></i>
          </div>
          <div class="transaction-details">
            <p class="transaction-type">Optimize Fleet Operations</p>
          </div>
        </div>
      </div>

      <!-- Blank Deposit and Withdraw Buttons -->
      <div class="bottom-buttons" id="bottom-buttons">
        <button class="scrape-button" id="scrapeButton">AI Loads Finder</button>
        <button class="load-button" id="scrapeCurrentButton">
          Negotiate with AI Call
        </button>
      </div>
    </div>

    <script src="./popup.js"></script>
  </body>
</html>
