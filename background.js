const ADMIN_BASE_URL = 'http://44.201.229.151:5173';

chrome.runtime.onInstalled.addListener(() => {
    console.log('Xtrucker AI Extension Installed');
});

chrome.runtime.onMessage.addListener((message,) => {
    if (message.type === 'login-request') {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            
            console.log("Tabs Query Result:", tabs);

            if (!tabs || tabs.length === 0) {
                console.error("❌ No active tab found in browser.");
                return;
            }

            const activeTab = tabs[0];

            // Check if the tab's URL is valid
            if (!activeTab.url) {
                console.error('Tab does not have a valid URL.');
                return;
            }

            // Check if the tab's URL is not a chrome:// page
            if (activeTab.url.startsWith('chrome://')) {
                console.error('Cannot access chrome:// URLs');
                return;
            }
            const EXTENSION_ID = chrome.runtime.id;
            console.log("Extension ID:", EXTENSION_ID);
            chrome.tabs.update(activeTab.id, {
                url: `${ADMIN_BASE_URL}/auth/signin?extension=true&extension_id=${EXTENSION_ID}`,
            });
        });
    }
});

// HANDLE INTENTS
chrome.runtime.onMessageExternal.addListener((request, sender, sendResponse) => {
    if (request) {
        switch (request.type) {
            case "login-request":
                // Save user data to storage
                chrome.storage.local.set({ user: request.data }, () => {
                    if (chrome.runtime.lastError) {
                        console.error("Error storing data:", chrome.runtime.lastError);
                        sendResponse({ success: false, message: "Failed to store user data" });
                        return;
                    }
                    console.log("Data stored in Chrome storage:", request.data);
                    sendResponse({ success: true, message: "User logged in" });
                });
                // Important to return true to indicate that sendResponse will be called asynchronously
                return true;
            default:
                console.log("Unknown intent from external resource");
                sendResponse({ success: false, message: "Unknown request type" });
        }
    }
});